# Enterprise Portfolio Management System

## Project Structure

This enterprise-level Next.js application follows Domain-Driven Design (DDD) principles and hexagonal architecture patterns for maximum scalability, maintainability, and testability.

### Architecture Overview

- **Domain Layer**: Core business logic (trading, portfolios, users, brokers)
- **Application Layer**: Use cases and orchestration
- **Infrastructure Layer**: External integrations and data persistence
- **Presentation Layer**: UI components and user interfaces

### Key Features Supported

- Multi-broker connection support
- Copy trading functionality (master → child accounts)
- User invitation system
- Real-time trade synchronization
- Dashboard and portfolio analysis
- Modular broker integrations
- Enterprise-level scalability

### Technology Stack

- Next.js 14+ with App Router
- Supabase for backend/database
- shadcn/ui for UI components
- Framer Motion for animations
- Optimistic UI patterns

## Folder Structure

The project follows enterprise-level organization with clear separation of concerns and domain-driven design principles.

```
arthik.ai/
├── app/                                    # Next.js App Router
│   ├── (auth)/                            # Authentication routes group
│   │   ├── login/
│   │   ├── register/
│   │   ├── forgot-password/
│   │   └── reset-password/
│   ├── (dashboard)/                       # Protected dashboard routes
│   │   ├── dashboard/                     # Main dashboard
│   │   ├── portfolio/                     # Portfolio management
│   │   ├── trading/                       # Trading interface
│   │   ├── copy-trading/                  # Copy trading features
│   │   │   ├── master/                    # Master account management
│   │   │   ├── child/                     # Child account management
│   │   │   └── invitations/               # Invitation system
│   │   ├── analytics/                     # Analytics and reporting
│   │   ├── settings/                      # User settings
│   │   └── brokers/                       # Broker connections
│   ├── (public)/                          # Public routes
│   │   ├── about/
│   │   ├── pricing/
│   │   ├── contact/
│   │   └── legal/
│   ├── api/                               # API routes
│   │   ├── auth/                          # Authentication endpoints
│   │   ├── trading/                       # Trading operations
│   │   ├── portfolio/                     # Portfolio operations
│   │   ├── brokers/                       # Broker integrations
│   │   ├── copy-trading/                  # Copy trading logic
│   │   ├── analytics/                     # Analytics endpoints
│   │   ├── webhooks/                      # Webhook handlers
│   │   └── real-time/                     # Real-time data endpoints
│   ├── layout.tsx                         # Root layout
│   ├── page.tsx                           # Home page
│   └── globals.css                        # Global styles
│
├── src/                                   # Source code
│   ├── domain/                            # Domain Layer (Business Logic)
│   │   ├── trading/                       # Trading domain
│   │   │   ├── entities/                  # Trade, Order, Position entities
│   │   │   ├── value-objects/             # Price, Quantity, Symbol VOs
│   │   │   ├── repositories/              # Trading repository interfaces
│   │   │   ├── services/                  # Trading domain services
│   │   │   ├── events/                    # Trading domain events
│   │   │   ├── exceptions/                # Trading-specific exceptions
│   │   │   ├── enums/                     # OrderType, OrderStatus enums
│   │   │   └── types/                     # Trading type definitions
│   │   ├── portfolio/                     # Portfolio domain
│   │   │   ├── entities/                  # Portfolio, Holding entities
│   │   │   ├── value-objects/             # Performance, Allocation VOs
│   │   │   ├── repositories/              # Portfolio repository interfaces
│   │   │   ├── services/                  # Portfolio domain services
│   │   │   ├── events/                    # Portfolio domain events
│   │   │   ├── exceptions/                # Portfolio-specific exceptions
│   │   │   ├── enums/                     # AssetType, AllocationStrategy
│   │   │   └── types/                     # Portfolio type definitions
│   │   ├── user/                          # User domain
│   │   │   ├── entities/                  # User, Account entities
│   │   │   ├── value-objects/             # Email, UserId VOs
│   │   │   ├── repositories/              # User repository interfaces
│   │   │   ├── services/                  # User domain services
│   │   │   ├── events/                    # User domain events
│   │   │   ├── exceptions/                # User-specific exceptions
│   │   │   ├── enums/                     # UserRole, AccountType enums
│   │   │   └── types/                     # User type definitions
│   │   ├── broker/                        # Broker domain
│   │   │   ├── entities/                  # BrokerConnection, Credentials
│   │   │   ├── value-objects/             # BrokerConfig, ApiKey VOs
│   │   │   ├── repositories/              # Broker repository interfaces
│   │   │   ├── services/                  # Broker domain services
│   │   │   ├── events/                    # Broker domain events
│   │   │   ├── exceptions/                # Broker-specific exceptions
│   │   │   ├── enums/                     # BrokerType, ConnectionStatus
│   │   │   └── types/                     # Broker type definitions
│   │   ├── notification/                  # Notification domain
│   │   │   ├── entities/                  # Notification, Alert entities
│   │   │   ├── value-objects/             # NotificationContent VOs
│   │   │   ├── repositories/              # Notification repositories
│   │   │   ├── services/                  # Notification services
│   │   │   ├── events/                    # Notification events
│   │   │   ├── exceptions/                # Notification exceptions
│   │   │   ├── enums/                     # NotificationType, Priority
│   │   │   └── types/                     # Notification types
│   │   ├── analytics/                     # Analytics domain
│   │   │   ├── entities/                  # Report, Metric entities
│   │   │   ├── value-objects/             # PerformanceMetric VOs
│   │   │   ├── repositories/              # Analytics repositories
│   │   │   ├── services/                  # Analytics services
│   │   │   ├── events/                    # Analytics events
│   │   │   ├── exceptions/                # Analytics exceptions
│   │   │   ├── enums/                     # ReportType, MetricType
│   │   │   └── types/                     # Analytics types
│   │   └── shared/                        # Shared domain concepts
│   │       ├── value-objects/             # Money, DateTime, Id VOs
│   │       ├── exceptions/                # Base domain exceptions
│   │       ├── enums/                     # Common enums
│   │       ├── types/                     # Shared type definitions
│   │       ├── constants/                 # Domain constants
│   │       └── utils/                     # Domain utilities
│   │
│   ├── application/                       # Application Layer (Use Cases)
│   │   ├── use-cases/                     # Application use cases
│   │   │   ├── trading/                   # Trading use cases
│   │   │   ├── portfolio/                 # Portfolio use cases
│   │   │   ├── user/                      # User management use cases
│   │   │   ├── broker/                    # Broker integration use cases
│   │   │   ├── analytics/                 # Analytics use cases
│   │   │   ├── copy-trading/              # Copy trading use cases
│   │   │   └── notifications/             # Notification use cases
│   │   ├── services/                      # Application services
│   │   ├── dto/                           # Data Transfer Objects
│   │   ├── mappers/                       # Domain ↔ DTO mappers
│   │   ├── validators/                    # Input validation
│   │   └── events/                        # Application events
│   │
│   ├── infrastructure/                    # Infrastructure Layer
│   │   ├── database/                      # Database implementations
│   │   │   ├── supabase/                  # Supabase client config
│   │   │   ├── repositories/              # Repository implementations
│   │   │   ├── migrations/                # Database migrations
│   │   │   ├── seeds/                     # Database seeds
│   │   │   └── schemas/                   # Database schemas
│   │   ├── brokers/                       # Broker integrations
│   │   │   ├── adapters/                  # Broker adapters
│   │   │   ├── connectors/                # Connection management
│   │   │   ├── protocols/                 # Communication protocols
│   │   │   └── implementations/           # Specific broker implementations
│   │   │       ├── alpaca/                # Alpaca integration
│   │   │       ├── interactive-brokers/   # Interactive Brokers
│   │   │       ├── td-ameritrade/         # TD Ameritrade
│   │   │       ├── robinhood/             # Robinhood
│   │   │       └── fidelity/              # Fidelity
│   │   ├── external-apis/                 # External API integrations
│   │   ├── messaging/                     # Real-time messaging
│   │   │   ├── websockets/                # WebSocket connections
│   │   │   ├── real-time/                 # Real-time data handling
│   │   │   ├── events/                    # Event handling
│   │   │   └── queues/                    # Message queues
│   │   ├── storage/                       # File storage
│   │   ├── monitoring/                    # Monitoring and logging
│   │   └── auth/                          # Authentication providers
│   │
│   ├── presentation/                      # Presentation Layer
│   │   ├── components/                    # React components
│   │   │   ├── ui/                        # Base UI components (shadcn/ui)
│   │   │   ├── features/                  # Feature-specific components
│   │   │   │   ├── trading/               # Trading components
│   │   │   │   ├── portfolio/             # Portfolio components
│   │   │   │   ├── dashboard/             # Dashboard components
│   │   │   │   ├── copy-trading/          # Copy trading components
│   │   │   │   ├── analytics/             # Analytics components
│   │   │   │   ├── user-management/       # User management components
│   │   │   │   └── broker-connection/     # Broker connection components
│   │   │   ├── layout/                    # Layout components
│   │   │   ├── forms/                     # Form components
│   │   │   ├── charts/                    # Chart components
│   │   │   └── tables/                    # Table components
│   │   ├── hooks/                         # Custom React hooks
│   │   ├── providers/                     # Context providers
│   │   ├── stores/                        # Client-side state management
│   │   ├── utils/                         # Presentation utilities
│   │   └── styles/                        # Styling utilities
│   │
│   └── shared/                            # Shared utilities
│       ├── types/                         # Shared TypeScript types
│       ├── constants/                     # Application constants
│       ├── utils/                         # Utility functions
│       ├── config/                        # Configuration
│       ├── errors/                        # Error handling
│       └── validators/                    # Validation schemas
│
├── public/                                # Static assets
│   ├── images/                            # Image assets
│   ├── icons/                             # Icon assets
│   ├── documents/                         # Document assets
│   └── assets/                            # Other static assets
│
├── tests/                                 # Test suites
│   ├── unit/                              # Unit tests
│   │   ├── domain/                        # Domain layer tests
│   │   ├── application/                   # Application layer tests
│   │   ├── infrastructure/                # Infrastructure tests
│   │   └── presentation/                  # Presentation tests
│   ├── integration/                       # Integration tests
│   ├── e2e/                               # End-to-end tests
│   ├── fixtures/                          # Test fixtures
│   ├── mocks/                             # Test mocks
│   └── utils/                             # Test utilities
│
├── scripts/                               # Build and deployment scripts
│   ├── build/                             # Build scripts
│   ├── deploy/                            # Deployment scripts
│   ├── database/                          # Database scripts
│   └── development/                       # Development scripts
│
├── config/                                # Configuration files
│   ├── environments/                      # Environment configs
│   ├── database/                          # Database configs
│   ├── brokers/                           # Broker configs
│   └── monitoring/                        # Monitoring configs
│
├── docs/                                  # Documentation
│   ├── api/                               # API documentation
│   ├── architecture/                      # Architecture docs
│   ├── deployment/                        # Deployment guides
│   └── user-guides/                       # User documentation
│
├── .env.local                             # Environment variables
├── .env.example                           # Environment template
├── .gitignore                             # Git ignore rules
├── next.config.js                         # Next.js configuration
├── tailwind.config.js                     # Tailwind CSS config
├── tsconfig.json                          # TypeScript configuration
├── package.json                           # Dependencies and scripts
└── README.md                              # Project documentation
```

## Architecture Principles

### Domain-Driven Design (DDD)
- **Domain Layer**: Contains the core business logic and rules
- **Application Layer**: Orchestrates domain objects to fulfill use cases
- **Infrastructure Layer**: Handles external concerns (database, APIs, brokers)
- **Presentation Layer**: Manages user interface and user interactions

### Hexagonal Architecture
- **Ports**: Interfaces that define contracts (repositories, services)
- **Adapters**: Implementations that fulfill the contracts
- **Core**: Domain logic isolated from external dependencies

### Key Benefits

1. **Scalability**: Modular structure supports easy feature additions
2. **Maintainability**: Clear separation of concerns and responsibilities
3. **Testability**: Each layer can be tested independently
4. **Flexibility**: Easy to swap implementations (brokers, databases)
5. **Team Collaboration**: Different teams can work on different layers
6. **Business Focus**: Domain layer reflects actual business requirements

### Copy Trading Architecture

The copy trading system is designed with master-child relationships:

- **Master Accounts**: Execute trades that are automatically replicated
- **Child Accounts**: Receive and execute copied trades based on allocation rules
- **Invitation System**: Secure invitation and approval workflow
- **Real-time Sync**: Immediate trade replication with optimistic UI updates

### Broker Integration Strategy

Modular broker integration supports multiple trading platforms:

- **Adapter Pattern**: Consistent interface across different brokers
- **Protocol Abstraction**: Common trading operations regardless of broker
- **Configuration-Driven**: Easy addition of new broker implementations
- **Failover Support**: Graceful handling of broker connectivity issues

### Real-time Data Flow

1. **Broker Events**: Real-time market data and trade confirmations
2. **WebSocket Connections**: Persistent connections for live updates
3. **Event Processing**: Domain events trigger appropriate actions
4. **UI Updates**: Optimistic updates with real-time synchronization

This structure ensures enterprise-level scalability while maintaining clean code organization and supporting complex financial operations.
